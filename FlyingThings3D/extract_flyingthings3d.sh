#!/bin/bash

# 数据集目录
DATASET_DIR="/root/Datasets/FlyingThings3D"
# 解压目标目录
TARGET_DIR="${DATASET_DIR}/extracted"
# 日志文件
LOG_FILE="${DATASET_DIR}/extract.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 需要解压的文件列表
FILES=(
    "FlyingThings3D_subset_image_clean.tar.bz2"
    "FlyingThings3D_subset_flow.tar.bz2"
    "FlyingThings3D_subset_disparity.tar.bz2"
    "FlyingThings3D_subset_flow_occlusions.tar.bz2"
    "FlyingThings3D_subset_disparity_occlusions.tar.bz2"
    "FlyingThings3D_subset_disparity_change.tar.bz2"
)

mkdir -p "$TARGET_DIR"

log "开始解压FlyingThings3D数据集"
log "目标目录: $TARGET_DIR"

for FILE in "${FILES[@]}"; do
    FILEPATH="${DATASET_DIR}/${FILE}"
    if [ ! -f "$FILEPATH" ]; then
        log "文件不存在: $FILEPATH"
        continue
    fi
    
    # 检查是否已经解压过（简单检查目标目录中是否有相关文件）
    EXTRACTED_CHECK=$(find "$TARGET_DIR" -name "*.pfm" -o -name "*.png" -o -name "*.flo" | head -1)
    if [ -n "$EXTRACTED_CHECK" ]; then
        log "检测到已有解压文件，跳过 $FILE"
        continue
    fi
    
    log "正在解压: $FILE"
    FILESIZE=$(stat -c %s "$FILEPATH")
    log "文件大小: $(numfmt --to=iec-i --suffix=B --format="%.2f" $FILESIZE)"
    
    if pv -s $FILESIZE "$FILEPATH" | tar xj -C "$TARGET_DIR"; then
        log "$FILE 解压完成"
    else
        log "错误: $FILE 解压失败"
        exit 1
    fi
    log "-----------------------------"
done

log "全部解压完成！"
echo "全部解压完成！" 